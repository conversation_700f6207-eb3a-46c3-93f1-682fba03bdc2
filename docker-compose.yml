version: '3.3'

volumes:
  jmeter_db_data:

networks:
  jmeter-net:
    external: false

services:
  jmeter-db:
    image: postgres
    environment:
      POSTGRES_USER: jmeter
      POSTGRES_PASSWORD: jmeter
      POSTGRES_DB: jmeter
    ports:
      - "35434:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 30s
      timeout: 30s
      retries: 3
    restart: on-failure
    deploy:
      restart_policy:
        condition: on-failure
    stdin_open: true
    tty: true
    networks:
      - jmeter-net
    volumes:
      - jmeter_db_data:/var/lib/postgresql/data