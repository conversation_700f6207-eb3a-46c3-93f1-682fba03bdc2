/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.jmeter.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        email;
    private String        password;
    private String        name;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private Boolean       isActive;

    public Users() {}

    public Users(Users value) {
        this.id = value.id;
        this.email = value.email;
        this.password = value.password;
        this.name = value.name;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.isActive = value.isActive;
    }

    public Users(
        UUID          id,
        String        email,
        String        password,
        String        name,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        Boolean       isActive
    ) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.name = name;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.isActive = isActive;
    }

    /**
     * Getter for <code>users.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>users.id</code>.
     */
    public Users setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>users.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>users.email</code>.
     */
    public Users setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>users.password</code>.
     */
    public String getPassword() {
        return this.password;
    }

    /**
     * Setter for <code>users.password</code>.
     */
    public Users setPassword(String password) {
        this.password = password;
        return this;
    }

    /**
     * Getter for <code>users.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>users.name</code>.
     */
    public Users setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>users.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>users.created_on</code>.
     */
    public Users setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>users.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>users.updated_on</code>.
     */
    public Users setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>users.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>users.is_active</code>.
     */
    public Users setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Users other = (Users) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        }
        else if (!id.equals(other.id))
            return false;
        if (email == null) {
            if (other.email != null)
                return false;
        }
        else if (!email.equals(other.email))
            return false;
        if (password == null) {
            if (other.password != null)
                return false;
        }
        else if (!password.equals(other.password))
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        }
        else if (!name.equals(other.name))
            return false;
        if (createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!createdOn.equals(other.createdOn))
            return false;
        if (updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!updatedOn.equals(other.updatedOn))
            return false;
        if (isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!isActive.equals(other.isActive))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.password == null) ? 0 : this.password.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Users (");

        sb.append(id);
        sb.append(", ").append(email);
        sb.append(", ").append(password);
        sb.append(", ").append(name);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(isActive);

        sb.append(")");
        return sb.toString();
    }
}
