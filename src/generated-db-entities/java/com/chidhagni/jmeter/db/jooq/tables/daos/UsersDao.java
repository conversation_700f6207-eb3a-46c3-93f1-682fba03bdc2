/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.jmeter.db.jooq.tables.daos;


import com.chidhagni.jmeter.db.jooq.tables.Users;
import com.chidhagni.jmeter.db.jooq.tables.records.UsersRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class UsersDao extends DAOImpl<UsersRecord, com.chidhagni.jmeter.db.jooq.tables.pojos.Users, UUID> {

    /**
     * Create a new UsersDao without any configuration
     */
    public UsersDao() {
        super(Users.USERS, com.chidhagni.jmeter.db.jooq.tables.pojos.Users.class);
    }

    /**
     * Create a new UsersDao with an attached configuration
     */
    @Autowired
    public UsersDao(Configuration configuration) {
        super(Users.USERS, com.chidhagni.jmeter.db.jooq.tables.pojos.Users.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.jmeter.db.jooq.tables.pojos.Users object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Users.USERS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchById(UUID... values) {
        return fetch(Users.USERS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.jmeter.db.jooq.tables.pojos.Users fetchOneById(UUID value) {
        return fetchOne(Users.USERS.ID, value);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByEmail(String... values) {
        return fetch(Users.USERS.EMAIL, values);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public com.chidhagni.jmeter.db.jooq.tables.pojos.Users fetchOneByEmail(String value) {
        return fetchOne(Users.USERS.EMAIL, value);
    }

    /**
     * Fetch records that have <code>password BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfPassword(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.PASSWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>password IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByPassword(String... values) {
        return fetch(Users.USERS.PASSWORD, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByName(String... values) {
        return fetch(Users.USERS.NAME, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Users.USERS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Users.USERS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Users.USERS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.jmeter.db.jooq.tables.pojos.Users> fetchByIsActive(Boolean... values) {
        return fetch(Users.USERS.IS_ACTIVE, values);
    }
}
