/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.jmeter.db.jooq.tables.records;


import com.chidhagni.jmeter.db.jooq.tables.Users;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UsersRecord extends UpdatableRecordImpl<UsersRecord> implements Record7<UUID, String, String, String, LocalDateTime, LocalDateTime, Boolean> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>users.id</code>.
     */
    public UsersRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>users.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>users.email</code>.
     */
    public UsersRecord setEmail(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>users.email</code>.
     */
    public String getEmail() {
        return (String) get(1);
    }

    /**
     * Setter for <code>users.password</code>.
     */
    public UsersRecord setPassword(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>users.password</code>.
     */
    public String getPassword() {
        return (String) get(2);
    }

    /**
     * Setter for <code>users.name</code>.
     */
    public UsersRecord setName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>users.name</code>.
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>users.created_on</code>.
     */
    public UsersRecord setCreatedOn(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>users.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>users.updated_on</code>.
     */
    public UsersRecord setUpdatedOn(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>users.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>users.is_active</code>.
     */
    public UsersRecord setIsActive(Boolean value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>users.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, String, String, String, LocalDateTime, LocalDateTime, Boolean> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, String, String, String, LocalDateTime, LocalDateTime, Boolean> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Users.USERS.ID;
    }

    @Override
    public Field<String> field2() {
        return Users.USERS.EMAIL;
    }

    @Override
    public Field<String> field3() {
        return Users.USERS.PASSWORD;
    }

    @Override
    public Field<String> field4() {
        return Users.USERS.NAME;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return Users.USERS.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return Users.USERS.UPDATED_ON;
    }

    @Override
    public Field<Boolean> field7() {
        return Users.USERS.IS_ACTIVE;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getEmail();
    }

    @Override
    public String component3() {
        return getPassword();
    }

    @Override
    public String component4() {
        return getName();
    }

    @Override
    public LocalDateTime component5() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component6() {
        return getUpdatedOn();
    }

    @Override
    public Boolean component7() {
        return getIsActive();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getEmail();
    }

    @Override
    public String value3() {
        return getPassword();
    }

    @Override
    public String value4() {
        return getName();
    }

    @Override
    public LocalDateTime value5() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value6() {
        return getUpdatedOn();
    }

    @Override
    public Boolean value7() {
        return getIsActive();
    }

    @Override
    public UsersRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public UsersRecord value2(String value) {
        setEmail(value);
        return this;
    }

    @Override
    public UsersRecord value3(String value) {
        setPassword(value);
        return this;
    }

    @Override
    public UsersRecord value4(String value) {
        setName(value);
        return this;
    }

    @Override
    public UsersRecord value5(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public UsersRecord value6(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public UsersRecord value7(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public UsersRecord values(UUID value1, String value2, String value3, String value4, LocalDateTime value5, LocalDateTime value6, Boolean value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UsersRecord
     */
    public UsersRecord() {
        super(Users.USERS);
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(UUID id, String email, String password, String name, LocalDateTime createdOn, LocalDateTime updatedOn, Boolean isActive) {
        super(Users.USERS);

        setId(id);
        setEmail(email);
        setPassword(password);
        setName(name);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setIsActive(isActive);
    }
}
