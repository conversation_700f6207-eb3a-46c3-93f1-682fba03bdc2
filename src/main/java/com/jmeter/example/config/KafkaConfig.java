package com.jmeter.example.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

/**
 * Kafka configuration class for setting up topics and other Kafka-related beans.
 * 
 * This configuration ensures that required Kafka topics are created automatically
 * when the application starts.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Configuration
public class KafkaConfig {

    @Value("${kafka.topic.email}")
    private String emailTopic;

    /**
     * Creates the email notifications topic with appropriate configuration.
     * 
     * @return NewTopic configuration for email notifications
     */
    @Bean
    public NewTopic emailNotificationTopic() {
        return TopicBuilder.name(emailTopic)
                .partitions(3)
                .replicas(1)
                .build();
    }
}
