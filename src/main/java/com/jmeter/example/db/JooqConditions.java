package com.jmeter.example.db;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.SortField;

import static org.jooq.impl.DSL.noCondition;

public class JooqConditions {
    private JooqConditions() {
    }

    public static <T> Condition eqIfPresent(Field<T> field, T value) {
        return value != null ? field.eq(value) : noCondition();
    }

    public static <T> SortField sortField(Field<T> field, String sortOrder){
        if ("asc".equalsIgnoreCase(sortOrder)) {
            return field.asc();
        }
        return field.desc();
    }
}
