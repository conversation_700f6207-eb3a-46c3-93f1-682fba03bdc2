package com.jmeter.example.controller;

import com.jmeter.example.dto.BulkEmailRequest;
import com.jmeter.example.dto.EmailRequest;
import com.jmeter.example.dto.EmailResponse;
import com.jmeter.example.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * REST controller for email operations including individual emails
 * and bulk email campaigns.
 * 
 * This controller provides endpoints for sending emails synchronously,
 * queuing emails for asynchronous processing, and managing bulk email campaigns.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/email")
@RequiredArgsConstructor
public class EmailController {

    private final EmailService emailService;

    /**
     * Sends a single email synchronously.
     * 
     * @param emailRequest the email request containing recipient and content details
     * @return ResponseEntity containing the email response
     */
    @PostMapping("/send")
    public ResponseEntity<EmailResponse> sendEmail(@Valid @RequestBody EmailRequest emailRequest) {
        log.info("Received request to send email to: {}", emailRequest.getTo());
        
        EmailResponse response = emailService.sendEmail(emailRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * Queues an email for asynchronous processing via Kafka.
     * 
     * @param emailRequest the email request to be queued
     * @return ResponseEntity containing the queuing response
     */
    @PostMapping("/queue")
    public ResponseEntity<EmailResponse> queueEmail(@Valid @RequestBody EmailRequest emailRequest) {
        log.info("Received request to queue email for: {}", emailRequest.getTo());
        
        EmailResponse response = emailService.queueEmail(emailRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * Sends bulk emails to users registered in a specific year.
     * 
     * @param bulkEmailRequest the bulk email request containing campaign details
     * @return ResponseEntity containing the bulk email response
     */
    @PostMapping("/bulk/yearly")
    public ResponseEntity<EmailResponse> sendBulkEmailToYearlyUsers(
            @Valid @RequestBody BulkEmailRequest bulkEmailRequest) {
        
        log.info("Received bulk email request for users registered in year: {}", 
                bulkEmailRequest.getTargetYear());
        
        EmailResponse response = emailService.sendBulkEmailToYearlyUsers(bulkEmailRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * Sends bulk emails to users registered in the current year.
     * This is a convenience endpoint that automatically uses the current year.
     * 
     * @param subject the email subject
     * @param templateName the email template name
     * @return ResponseEntity containing the bulk email response
     */
    @PostMapping("/bulk/current-year")
    public ResponseEntity<EmailResponse> sendBulkEmailToCurrentYearUsers(
            @RequestParam String subject,
            @RequestParam String templateName,
            @RequestParam(defaultValue = "true") Boolean activeUsersOnly) {
        
        int currentYear = LocalDate.now().getYear();
        log.info("Received bulk email request for current year users: {}", currentYear);
        
        // Create common template variables
        Map<String, Object> commonVariables = new HashMap<>();
        commonVariables.put("currentYear", currentYear);
        commonVariables.put("companyName", "JMeter Application");
        
        BulkEmailRequest bulkEmailRequest = BulkEmailRequest.builder()
                .subject(subject)
                .templateName(templateName)
                .targetYear(currentYear)
                .activeUsersOnly(activeUsersOnly)
                .commonTemplateVariables(commonVariables)
                .priority(EmailRequest.EmailPriority.NORMAL)
                .build();
        
        EmailResponse response = emailService.sendBulkEmailToYearlyUsers(bulkEmailRequest);
        return ResponseEntity.ok(response);
    }

    /**
     * Gets the count of users for a specific registration year.
     * 
     * @param year the year to count users for
     * @param activeOnly whether to count only active users
     * @return ResponseEntity containing user count information
     */
    @GetMapping("/users/count")
    public ResponseEntity<Map<String, Object>> getUserCountByYear(
            @RequestParam int year,
            @RequestParam(defaultValue = "true") Boolean activeOnly) {
        
        log.info("Received request to count users for year: {}, activeOnly: {}", year, activeOnly);
        
        int userCount = emailService.getUserCountByYear(year, activeOnly);
        
        Map<String, Object> response = new HashMap<>();
        response.put("year", year);
        response.put("activeOnly", activeOnly);
        response.put("userCount", userCount);
        response.put("message", String.format("Found %d users registered in %d", userCount, year));
        
        return ResponseEntity.ok(response);
    }

    /**
     * Gets the count of users registered in the current year.
     * 
     * @param activeOnly whether to count only active users
     * @return ResponseEntity containing current year user count
     */
    @GetMapping("/users/count/current-year")
    public ResponseEntity<Map<String, Object>> getCurrentYearUserCount(
            @RequestParam(defaultValue = "true") Boolean activeOnly) {
        
        int currentYear = LocalDate.now().getYear();
        return getUserCountByYear(currentYear, activeOnly);
    }

    /**
     * Health check endpoint for the email service.
     * 
     * @return ResponseEntity indicating service health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "Email Service");
        health.put("timestamp", LocalDate.now().toString());
        
        return ResponseEntity.ok(health);
    }
}
