package com.jmeter.example.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Data Transfer Object for email requests.
 * 
 * This class represents the structure of email requests that can be sent
 * through the email service, including recipient information, subject,
 * template details, and any dynamic content.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest {

    /**
     * Recipient's email address.
     */
    @NotBlank(message = "Recipient email is required")
    private String to;

    /**
     * Recipient's name for personalization.
     */
    private String recipientName;

    /**
     * Email subject line.
     */
    @NotBlank(message = "Email subject is required")
    private String subject;

    /**
     * Email template name to use for rendering.
     */
    @NotBlank(message = "Template name is required")
    private String templateName;

    /**
     * Dynamic variables to be replaced in the email template.
     */
    private Map<String, Object> templateVariables;

    /**
     * Priority level for email processing.
     */
    @Builder.Default
    private EmailPriority priority = EmailPriority.NORMAL;

    /**
     * Email priority levels for processing order.
     */
    public enum EmailPriority {
        LOW, NORMAL, HIGH, URGENT
    }
}
