package com.jmeter.example.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Data Transfer Object for email responses.
 * 
 * This class represents the response structure for email operations,
 * providing information about the email processing status and results.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailResponse {

    /**
     * Unique identifier for the email request.
     */
    private String emailId;

    /**
     * Status of the email processing.
     */
    private EmailStatus status;

    /**
     * Descriptive message about the email processing result.
     */
    private String message;

    /**
     * Timestamp when the email was processed.
     */
    private LocalDateTime processedAt;

    /**
     * Number of emails queued for bulk operations.
     */
    private Integer emailsQueued;

    /**
     * Email processing status enumeration.
     */
    public enum EmailStatus {
        QUEUED,
        PROCESSING,
        SENT,
        FAILED,
        REJECTED
    }
}
