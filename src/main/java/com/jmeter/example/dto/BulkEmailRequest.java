package com.jmeter.example.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Data Transfer Object for bulk email requests.
 * 
 * This class represents the structure for sending bulk emails to multiple
 * recipients, typically used for marketing campaigns or notifications.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkEmailRequest {

    /**
     * Email subject line for all recipients.
     */
    @NotBlank(message = "Email subject is required")
    private String subject;

    /**
     * Email template name to use for rendering.
     */
    @NotBlank(message = "Template name is required")
    private String templateName;

    /**
     * Common template variables for all recipients.
     */
    private Map<String, Object> commonTemplateVariables;

    /**
     * Target year for filtering users (e.g., 2024 for users registered in 2024).
     */
    @NotNull(message = "Target year is required")
    private Integer targetYear;

    /**
     * Priority level for email processing.
     */
    @Builder.Default
    private EmailRequest.EmailPriority priority = EmailRequest.EmailPriority.NORMAL;

    /**
     * Whether to include only active users.
     */
    @Builder.Default
    private Boolean activeUsersOnly = true;
}
