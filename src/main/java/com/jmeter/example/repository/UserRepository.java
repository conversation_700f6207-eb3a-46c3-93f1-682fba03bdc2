package com.jmeter.example.repository;

import com.jmeter.example.model.User;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.chidhagni.jmeter.db.jooq.tables.Users.USERS;


@Repository
public class UserRepository {
    
    private final DSLContext dslContext;
    
    public UserRepository(DSLContext dslContext) {
        this.dslContext = dslContext;
    }
    
    public User save(User user) {
        if (user.getId() == null) {
            user.setId(UUID.randomUUID());
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (user.getCreatedOn() == null) {
            user.setCreatedOn(now);
        }
        user.setUpdatedOn(now);
        
        dslContext.insertInto(USERS)
                .set(USERS.ID, user.getId())
                .set(USERS.EMAIL, user.getEmail())
                .set(USERS.PASSWORD, user.getPassword())
                .set(USERS.NAME, user.getName())
                .set(USERS.CREATED_ON, user.getCreatedOn())
                .set(USERS.UPDATED_ON, user.getUpdatedOn())
                .set(USERS.IS_ACTIVE, user.isActive())
                .execute();
        
        return user;
    }
    
    public Optional<User> findByEmail(String email) {
        return dslContext.selectFrom(USERS)
                .where(USERS.EMAIL.eq(email))
                .fetchOptional()
                .map(r -> User.builder()
                        .id(r.getId())
                        .email(r.getEmail())
                        .password(r.getPassword())
                        .name(r.getName())
                        .createdOn(r.getCreatedOn())
                        .updatedOn(r.getUpdatedOn())
                        .active(r.getIsActive())
                        .build());
    }

    /**
     * Finds all users registered in the specified year.
     *
     * @param year the year to filter users by (e.g., 2024)
     * @param activeOnly whether to include only active users
     * @return list of users registered in the specified year
     */
    public List<User> findUsersByRegistrationYear(int year, boolean activeOnly) {
        var query = dslContext.selectFrom(USERS)
                .where(USERS.CREATED_ON.year().eq(year));

        if (activeOnly) {
            query = query.and(USERS.IS_ACTIVE.eq(true));
        }

        return query.fetch()
                .map(r -> User.builder()
                        .id(r.getId())
                        .email(r.getEmail())
                        .password(r.getPassword())
                        .name(r.getName())
                        .createdOn(r.getCreatedOn())
                        .updatedOn(r.getUpdatedOn())
                        .active(r.getIsActive())
                        .build());
    }

    /**
     * Counts the number of users registered in the specified year.
     *
     * @param year the year to count users for
     * @param activeOnly whether to count only active users
     * @return count of users registered in the specified year
     */
    public int countUsersByRegistrationYear(int year, boolean activeOnly) {
        var query = dslContext.selectCount()
                .from(USERS)
                .where(USERS.CREATED_ON.year().eq(year));

        if (activeOnly) {
            query = query.and(USERS.IS_ACTIVE.eq(true));
        }

        return query.fetchOne(0, int.class);
    }
}