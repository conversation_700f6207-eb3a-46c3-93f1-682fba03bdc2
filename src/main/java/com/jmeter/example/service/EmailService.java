package com.jmeter.example.service;

import com.jmeter.example.dto.BulkEmailRequest;
import com.jmeter.example.dto.EmailRequest;
import com.jmeter.example.dto.EmailResponse;
import com.jmeter.example.model.User;
import com.jmeter.example.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Service for handling email operations including sending individual emails
 * and bulk email campaigns.
 *
 * This service integrates with JavaMailSender for email delivery and
 * Thymeleaf for email template processing.
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    private final UserRepository userRepository;
    private final KafkaProducerService kafkaProducerService;

    @Value("${app.email.from}")
    private String fromEmail;

    @Value("${app.email.enabled:true}")
    private boolean emailEnabled;

    /**
     * Sends a single email directly (synchronous).
     *
     * @param emailRequest the email request containing recipient and content details
     * @return EmailResponse indicating the result of the email sending operation
     */
    public EmailResponse sendEmail(EmailRequest emailRequest) {
        log.info("Attempting to send email to: {}", emailRequest.getTo());

        if (!emailEnabled) {
            log.warn("Email sending is disabled. Skipping email to: {}", emailRequest.getTo());
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.REJECTED)
                    .message("Email sending is disabled")
                    .processedAt(LocalDateTime.now())
                    .build();
        }

        try {
            MimeMessage message = createMimeMessage(emailRequest);
            mailSender.send(message);

            log.info("Successfully sent email to: {}", emailRequest.getTo());
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.SENT)
                    .message("Email sent successfully")
                    .processedAt(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("Failed to send email to: {}", emailRequest.getTo(), e);
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.FAILED)
                    .message("Failed to send email: " + e.getMessage())
                    .processedAt(LocalDateTime.now())
                    .build();
        }
    }

    /**
     * Queues an email for asynchronous processing via Kafka.
     *
     * @param emailRequest the email request to be queued
     * @return EmailResponse indicating the queuing result
     */
    public EmailResponse queueEmail(EmailRequest emailRequest) {
        log.info("Queuing email for recipient: {}", emailRequest.getTo());

        boolean queued = kafkaProducerService.sendEmailRequest(emailRequest);

        if (queued) {
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.QUEUED)
                    .message("Email queued for processing")
                    .processedAt(LocalDateTime.now())
                    .build();
        } else {
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.FAILED)
                    .message("Failed to queue email")
                    .processedAt(LocalDateTime.now())
                    .build();
        }
    }

    /**
     * Sends bulk emails to users registered in the specified year.
     *
     * @param bulkEmailRequest the bulk email request containing campaign details
     * @return EmailResponse with information about the bulk email operation
     */
    @Transactional(readOnly = true)
    public EmailResponse sendBulkEmailToYearlyUsers(BulkEmailRequest bulkEmailRequest) {
        log.info("Starting bulk email campaign for users registered in year: {}",
                bulkEmailRequest.getTargetYear());

        try {
            List<User> targetUsers = userRepository.findUsersByRegistrationYear(
                    bulkEmailRequest.getTargetYear(),
                    bulkEmailRequest.getActiveUsersOnly()
            );

            if (targetUsers.isEmpty()) {
                log.warn("No users found for year: {}", bulkEmailRequest.getTargetYear());
                return EmailResponse.builder()
                        .emailId(UUID.randomUUID().toString())
                        .status(EmailResponse.EmailStatus.REJECTED)
                        .message("No users found for the specified year")
                        .processedAt(LocalDateTime.now())
                        .emailsQueued(0)
                        .build();
            }

            int queuedCount = 0;
            for (User user : targetUsers) {
                EmailRequest emailRequest = createEmailRequestForUser(user, bulkEmailRequest);
                if (kafkaProducerService.sendEmailRequest(emailRequest)) {
                    queuedCount++;
                }
            }

            log.info("Bulk email campaign completed. Queued {}/{} emails for year: {}",
                    queuedCount, targetUsers.size(), bulkEmailRequest.getTargetYear());

            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.QUEUED)
                    .message(String.format("Bulk email campaign queued for %d users", queuedCount))
                    .processedAt(LocalDateTime.now())
                    .emailsQueued(queuedCount)
                    .build();

        } catch (Exception e) {
            log.error("Failed to process bulk email campaign for year: {}",
                    bulkEmailRequest.getTargetYear(), e);
            return EmailResponse.builder()
                    .emailId(UUID.randomUUID().toString())
                    .status(EmailResponse.EmailStatus.FAILED)
                    .message("Failed to process bulk email campaign: " + e.getMessage())
                    .processedAt(LocalDateTime.now())
                    .emailsQueued(0)
                    .build();
        }
    }

    /**
     * Creates a MimeMessage from an EmailRequest.
     *
     * @param emailRequest the email request containing message details
     * @return configured MimeMessage ready to be sent
     * @throws MessagingException if there's an error creating the message
     */
    private MimeMessage createMimeMessage(EmailRequest emailRequest) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setFrom(fromEmail);
        helper.setTo(emailRequest.getTo());
        helper.setSubject(emailRequest.getSubject());

        String htmlContent = processTemplate(emailRequest.getTemplateName(),
                emailRequest.getTemplateVariables());
        helper.setText(htmlContent, true);

        return message;
    }

    /**
     * Processes an email template with the provided variables.
     *
     * @param templateName the name of the template to process
     * @param variables the variables to substitute in the template
     * @return processed HTML content
     */
    private String processTemplate(String templateName, Map<String, Object> variables) {
        Context context = new Context();
        if (variables != null) {
            context.setVariables(variables);
        }
        return templateEngine.process(templateName, context);
    }

    /**
     * Creates an EmailRequest for a specific user from a bulk email request.
     *
     * @param user the target user
     * @param bulkEmailRequest the bulk email campaign details
     * @return EmailRequest configured for the specific user
     */
    private EmailRequest createEmailRequestForUser(User user, BulkEmailRequest bulkEmailRequest) {
        Map<String, Object> templateVariables = new HashMap<>();

        // Add user-specific variables
        templateVariables.put("userName", user.getName());
        templateVariables.put("userEmail", user.getEmail());
        templateVariables.put("registrationYear", bulkEmailRequest.getTargetYear());

        // Add common variables from bulk request
        if (bulkEmailRequest.getCommonTemplateVariables() != null) {
            templateVariables.putAll(bulkEmailRequest.getCommonTemplateVariables());
        }

        return EmailRequest.builder()
                .to(user.getEmail())
                .recipientName(user.getName())
                .subject(bulkEmailRequest.getSubject())
                .templateName(bulkEmailRequest.getTemplateName())
                .templateVariables(templateVariables)
                .priority(bulkEmailRequest.getPriority())
                .build();
    }

    /**
     * Gets the count of users for a specific registration year.
     *
     * @param year the year to count users for
     * @param activeOnly whether to count only active users
     * @return count of users registered in the specified year
     */
    @Transactional(readOnly = true)
    public int getUserCountByYear(int year, boolean activeOnly) {
        return userRepository.countUsersByRegistrationYear(year, activeOnly);
    }
}
    }
