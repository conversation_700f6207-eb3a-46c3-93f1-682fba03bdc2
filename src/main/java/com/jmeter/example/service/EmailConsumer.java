package com.jmeter.example.service;

import com.jmeter.example.dto.EmailRequest;
import com.jmeter.example.dto.EmailResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

/**
 * Kafka consumer service for processing email requests asynchronously.
 * 
 * This service listens to email notification topics and processes
 * email requests by sending them through the email service.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailConsumer {

    private final EmailService emailService;

    /**
     * Consumes email requests from the Kafka email topic and processes them.
     * 
     * This method is automatically invoked when messages are available in the
     * email notifications topic. It processes each email request and sends
     * the email through the email service.
     * 
     * @param emailRequest the email request to process
     * @param partition the Kafka partition from which the message was consumed
     * @param offset the offset of the message in the partition
     * @param acknowledgment the acknowledgment object for manual commit
     */
    @KafkaListener(topics = "${kafka.topic.email}", groupId = "${spring.kafka.consumer.group-id}")
    public void consumeEmailRequest(
            @Payload EmailRequest emailRequest,
            @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {

        log.info("Received email request from Kafka - Partition: {}, Offset: {}, Recipient: {}", 
                partition, offset, emailRequest.getTo());

        try {
            // Process the email request
            EmailResponse response = emailService.sendEmail(emailRequest);
            
            if (response.getStatus() == EmailResponse.EmailStatus.SENT) {
                log.info("Successfully processed email request for recipient: {} - Status: {}", 
                        emailRequest.getTo(), response.getStatus());
            } else {
                log.warn("Email processing completed with status: {} for recipient: {} - Message: {}", 
                        response.getStatus(), emailRequest.getTo(), response.getMessage());
            }

            // Acknowledge successful processing
            acknowledgment.acknowledge();

        } catch (Exception e) {
            log.error("Failed to process email request for recipient: {} from partition: {}, offset: {}", 
                    emailRequest.getTo(), partition, offset, e);
            
            // In a production environment, you might want to:
            // 1. Send to a dead letter queue
            // 2. Implement retry logic
            // 3. Store failed messages for manual processing
            
            // For now, we'll acknowledge to prevent infinite retries
            acknowledgment.acknowledge();
        }
    }

    /**
     * Handles any processing errors that occur during email consumption.
     * 
     * This method can be used to implement custom error handling logic
     * such as sending failed messages to a dead letter queue.
     * 
     * @param emailRequest the email request that failed to process
     * @param exception the exception that occurred during processing
     */
    private void handleProcessingError(EmailRequest emailRequest, Exception exception) {
        log.error("Email processing error for recipient: {} - Error: {}", 
                emailRequest.getTo(), exception.getMessage());
        
        // TODO: Implement error handling strategies:
        // - Send to dead letter queue
        // - Store in database for retry
        // - Send notification to administrators
        // - Implement exponential backoff retry
    }
}
