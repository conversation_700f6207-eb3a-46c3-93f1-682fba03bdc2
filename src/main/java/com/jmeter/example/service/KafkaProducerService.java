package com.jmeter.example.service;

import com.jmeter.example.dto.EmailRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * Service for producing messages to Kafka topics.
 * 
 * This service handles the publishing of email requests to Kafka topics
 * for asynchronous processing by email consumers.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaProducerService {

    private final KafkaTemplate<String, Object> kafkaTemplate;

    @Value("${kafka.topic.email}")
    private String emailTopic;

    /**
     * Sends an email request to the Kafka email topic for asynchronous processing.
     * 
     * @param emailRequest the email request to be processed
     * @return true if the message was successfully sent to Kaf<PERSON>, false otherwise
     */
    public boolean sendEmailRequest(EmailRequest emailRequest) {
        try {
            log.info("Sending email request to Kafka topic: {} for recipient: {}", 
                    emailTopic, emailRequest.getTo());

            ListenableFuture<SendResult<String, Object>> future = 
                    kafkaTemplate.send(emailTopic, emailRequest.getTo(), emailRequest);

            future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                @Override
                public void onSuccess(SendResult<String, Object> result) {
                    log.info("Successfully sent email request to Kafka for recipient: {} with offset: {}", 
                            emailRequest.getTo(), result.getRecordMetadata().offset());
                }

                @Override
                public void onFailure(Throwable ex) {
                    log.error("Failed to send email request to Kafka for recipient: {}", 
                            emailRequest.getTo(), ex);
                }
            });

            return true;

        } catch (Exception e) {
            log.error("Error sending email request to Kafka for recipient: {}", 
                    emailRequest.getTo(), e);
            return false;
        }
    }

    /**
     * Sends multiple email requests to Kafka in batch.
     * 
     * @param emailRequests array of email requests to be processed
     * @return number of successfully sent messages
     */
    public int sendBulkEmailRequests(EmailRequest... emailRequests) {
        int successCount = 0;
        
        log.info("Sending {} email requests to Kafka topic: {}", emailRequests.length, emailTopic);
        
        for (EmailRequest emailRequest : emailRequests) {
            if (sendEmailRequest(emailRequest)) {
                successCount++;
            }
        }
        
        log.info("Successfully sent {}/{} email requests to Kafka", successCount, emailRequests.length);
        return successCount;
    }
}
