package com.jmeter.example.exception;

/**
 * Exception thrown when attempting to register a user with an email address
 * that already exists in the system.
 * 
 * This is a runtime exception that indicates a business rule violation
 * where user email addresses must be unique.
 * 
 * <AUTHOR>
 * @since 1.0
 */
public class UserAlreadyExistsException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * Constructs a new UserAlreadyExistsException with the specified detail message.
     * 
     * @param message the detail message explaining the cause of the exception
     */
    public UserAlreadyExistsException(String message) {
        super(message);
    }

    /**
     * Constructs a new UserAlreadyExistsException with the specified detail message
     * and cause.
     * 
     * @param message the detail message explaining the cause of the exception
     * @param cause the cause of the exception
     */
    public UserAlreadyExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new UserAlreadyExistsException with the specified cause.
     * 
     * @param cause the cause of the exception
     */
    public UserAlreadyExistsException(Throwable cause) {
        super(cause);
    }
}
