package com.jmeter.example.mcp.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jmeter.example.mcp.dto.McpRequest;
import com.jmeter.example.mcp.dto.McpResponse;
import com.jmeter.example.mcp.dto.McpTool;
import com.jmeter.example.mcp.service.McpKafkaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * WebSocket handler for MCP (Model Context Protocol) communication.
 * 
 * This handler manages WebSocket connections from AI assistants and
 * processes MCP requests for Kafka email operations.
 * 
 * <AUTHOR> <PERSON>du
 * @since 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpWebSocketHandler implements WebSocketHandler {

    private final McpKafkaService mcpKafkaService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("MCP WebSocket connection established: {}", session.getId());
        
        // Send initial capabilities and tool list
        sendCapabilities(session);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();
            log.info("MCP received message: {}", payload);
            
            try {
                McpRequest request = objectMapper.readValue(payload, McpRequest.class);
                McpResponse response = processRequest(request);
                
                String responseJson = objectMapper.writeValueAsString(response);
                session.sendMessage(new TextMessage(responseJson));
                
            } catch (Exception e) {
                log.error("Error processing MCP request", e);
                sendErrorResponse(session, "PARSE_ERROR", "Failed to parse request: " + e.getMessage(), null);
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("MCP WebSocket transport error for session: {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("MCP WebSocket connection closed: {} with status: {}", session.getId(), closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * Processes MCP requests and returns appropriate responses.
     */
    private McpResponse processRequest(McpRequest request) {
        try {
            switch (request.getMethod()) {
                case "tools/list":
                    return handleToolsList(request);
                case "tools/call":
                    return handleToolCall(request);
                case "initialize":
                    return handleInitialize(request);
                default:
                    return createErrorResponse(request.getId(), -32601, "Method not found: " + request.getMethod());
            }
        } catch (Exception e) {
            log.error("Error processing MCP request: {}", request.getMethod(), e);
            return createErrorResponse(request.getId(), -32603, "Internal error: " + e.getMessage());
        }
    }

    /**
     * Handles the tools/list request to provide available tools.
     */
    private McpResponse handleToolsList(McpRequest request) {
        List<McpTool> tools = Arrays.asList(
                McpTool.createEmailTool(),
                McpTool.createKafkaMonitorTool(),
                McpTool.createUserStatsTool()
        );

        Map<String, Object> result = new HashMap<>();
        result.put("tools", tools);

        return McpResponse.builder()
                .jsonrpc("2.0")
                .id(request.getId())
                .result(result)
                .build();
    }

    /**
     * Handles tool call requests.
     */
    private McpResponse handleToolCall(McpRequest request) {
        Map<String, Object> params = request.getParams();
        String toolName = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");

        Object result;
        switch (toolName) {
            case "send_bulk_email":
                result = mcpKafkaService.sendBulkEmail(arguments);
                break;
            case "monitor_kafka":
                result = mcpKafkaService.monitorKafka(arguments);
                break;
            case "get_user_stats":
                result = mcpKafkaService.getUserStats(arguments);
                break;
            default:
                return createErrorResponse(request.getId(), -32602, "Unknown tool: " + toolName);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("content", Arrays.asList(Map.of(
                "type", "text",
                "text", objectMapper.valueToTree(result).toString()
        )));

        return McpResponse.builder()
                .jsonrpc("2.0")
                .id(request.getId())
                .result(response)
                .build();
    }

    /**
     * Handles initialization request.
     */
    private McpResponse handleInitialize(McpRequest request) {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "1.0");
        result.put("serverInfo", Map.of(
                "name", "JMeter Kafka Email MCP Server",
                "version", "1.0.0"
        ));
        result.put("capabilities", Map.of(
                "tools", Map.of("listChanged", false),
                "resources", Map.of("subscribe", false, "listChanged", false)
        ));

        return McpResponse.builder()
                .jsonrpc("2.0")
                .id(request.getId())
                .result(result)
                .build();
    }

    /**
     * Sends server capabilities to the client.
     */
    private void sendCapabilities(WebSocketSession session) throws Exception {
        Map<String, Object> capabilities = new HashMap<>();
        capabilities.put("tools", Arrays.asList(
                McpTool.createEmailTool(),
                McpTool.createKafkaMonitorTool(),
                McpTool.createUserStatsTool()
        ));

        Map<String, Object> message = new HashMap<>();
        message.put("jsonrpc", "2.0");
        message.put("method", "notifications/tools/list_changed");
        message.put("params", capabilities);

        String json = objectMapper.writeValueAsString(message);
        session.sendMessage(new TextMessage(json));
    }

    /**
     * Creates an error response.
     */
    private McpResponse createErrorResponse(String id, int code, String message) {
        return McpResponse.builder()
                .jsonrpc("2.0")
                .id(id)
                .error(McpResponse.McpError.builder()
                        .code(code)
                        .message(message)
                        .build())
                .build();
    }

    /**
     * Sends an error response to the client.
     */
    private void sendErrorResponse(WebSocketSession session, String type, String message, String id) throws Exception {
        McpResponse errorResponse = createErrorResponse(id, -32700, message);
        String json = objectMapper.writeValueAsString(errorResponse);
        session.sendMessage(new TextMessage(json));
    }
}
