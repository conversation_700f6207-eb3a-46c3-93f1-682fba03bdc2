package com.jmeter.example.mcp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * MCP Tool definition for describing available tools to AI assistants.
 * 
 * This class represents a tool that can be called by AI assistants
 * through the MCP protocol.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpTool {

    /**
     * Unique name of the tool.
     */
    private String name;

    /**
     * Human-readable description of what the tool does.
     */
    private String description;

    /**
     * JSON Schema defining the input parameters for the tool.
     */
    private Map<String, Object> inputSchema;

    /**
     * Creates a tool definition for Kafka email operations.
     */
    public static McpTool createEmailTool() {
        return McpTool.builder()
                .name("send_bulk_email")
                .description("Send bulk emails to users registered in a specific year via Kafka")
                .inputSchema(Map.of(
                        "type", "object",
                        "properties", Map.of(
                                "subject", Map.of(
                                        "type", "string",
                                        "description", "Email subject line"
                                ),
                                "templateName", Map.of(
                                        "type", "string",
                                        "description", "Email template name"
                                ),
                                "targetYear", Map.of(
                                        "type", "integer",
                                        "description", "Year to filter users by (e.g., 2024)"
                                ),
                                "activeUsersOnly", Map.of(
                                        "type", "boolean",
                                        "description", "Whether to include only active users",
                                        "default", true
                                )
                        ),
                        "required", new String[]{"subject", "templateName", "targetYear"}
                ))
                .build();
    }

    /**
     * Creates a tool definition for Kafka monitoring.
     */
    public static McpTool createKafkaMonitorTool() {
        return McpTool.builder()
                .name("monitor_kafka")
                .description("Monitor Kafka topics, consumer groups, and message statistics")
                .inputSchema(Map.of(
                        "type", "object",
                        "properties", Map.of(
                                "operation", Map.of(
                                        "type", "string",
                                        "enum", new String[]{"topics", "consumers", "health", "stats"},
                                        "description", "Type of monitoring operation"
                                ),
                                "topicName", Map.of(
                                        "type", "string",
                                        "description", "Specific topic name (optional)"
                                )
                        ),
                        "required", new String[]{"operation"}
                ))
                .build();
    }

    /**
     * Creates a tool definition for user statistics.
     */
    public static McpTool createUserStatsTool() {
        return McpTool.builder()
                .name("get_user_stats")
                .description("Get user registration statistics by year")
                .inputSchema(Map.of(
                        "type", "object",
                        "properties", Map.of(
                                "year", Map.of(
                                        "type", "integer",
                                        "description", "Year to get statistics for"
                                ),
                                "activeOnly", Map.of(
                                        "type", "boolean",
                                        "description", "Whether to count only active users",
                                        "default", true
                                )
                        ),
                        "required", new String[]{"year"}
                ))
                .build();
    }
}
