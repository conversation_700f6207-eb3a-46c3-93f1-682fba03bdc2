package com.jmeter.example.mcp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MCP (Model Context Protocol) Response DTO.
 * 
 * This class represents the standard MCP response structure for
 * communication between the MCP server and AI assistants.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpResponse {

    /**
     * JSON-RPC version (always "2.0" for MCP).
     */
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";

    /**
     * Request identifier (matches the request ID).
     */
    private String id;

    /**
     * Successful response result.
     */
    private Object result;

    /**
     * Error information if the request failed.
     */
    private McpError error;

    /**
     * MCP Error structure.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class McpError {
        private int code;
        private String message;
        private Object data;
    }
}
