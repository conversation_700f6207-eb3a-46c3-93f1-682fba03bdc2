package com.jmeter.example.mcp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * MCP (Model Context Protocol) Request DTO.
 * 
 * This class represents the standard MCP request structure for
 * communication between AI assistants and the MCP server.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpRequest {

    /**
     * JSON-RPC version (always "2.0" for MCP).
     */
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";

    /**
     * Unique identifier for the request.
     */
    private String id;

    /**
     * The method/tool to be called.
     */
    private String method;

    /**
     * Parameters for the method call.
     */
    private Map<String, Object> params;

    /**
     * MCP protocol version.
     */
    @JsonProperty("mcpVersion")
    private String mcpVersion = "1.0";
}
