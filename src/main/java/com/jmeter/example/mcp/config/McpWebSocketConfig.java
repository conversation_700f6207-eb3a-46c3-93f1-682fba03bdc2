package com.jmeter.example.mcp.config;

import com.jmeter.example.mcp.handler.McpWebSocketHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * WebSocket configuration for MCP (Model Context Protocol) server.
 * 
 * This configuration sets up WebSocket endpoints for AI assistants
 * to connect and interact with the Kafka email system.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Configuration
@EnableWebSocket
@RequiredArgsConstructor
public class McpWebSocketConfig implements WebSocketConfigurer {

    private final McpWebSocketHandler mcpWebSocketHandler;

    @Value("${mcp.websocket.endpoint:/mcp}")
    private String mcpEndpoint;

    @Value("${mcp.websocket.allowed-origins:*}")
    private String allowedOrigins;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(mcpWebSocketHandler, mcpEndpoint)
                .setAllowedOrigins(allowedOrigins)
                .withSockJS(); // Enable SockJS fallback for better compatibility
    }
}
