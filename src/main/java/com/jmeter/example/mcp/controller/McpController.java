package com.jmeter.example.mcp.controller;

import com.jmeter.example.mcp.dto.McpRequest;
import com.jmeter.example.mcp.dto.McpResponse;
import com.jmeter.example.mcp.dto.McpTool;
import com.jmeter.example.mcp.service.McpKafkaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST controller for MCP (Model Context Protocol) operations.
 * 
 * This controller provides HTTP endpoints as an alternative to WebSocket
 * for AI assistants to interact with the Kafka email system.
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/mcp")
@RequiredArgsConstructor
public class McpController {

    private final McpKafkaService mcpKafkaService;

    /**
     * Lists available MCP tools.
     * 
     * @return ResponseEntity containing available tools
     */
    @GetMapping("/tools")
    public ResponseEntity<Map<String, Object>> listTools() {
        log.info("MCP: Listing available tools");

        List<McpTool> tools = Arrays.asList(
                McpTool.createEmailTool(),
                McpTool.createKafkaMonitorTool(),
                McpTool.createUserStatsTool()
        );

        Map<String, Object> response = new HashMap<>();
        response.put("tools", tools);
        response.put("count", tools.size());

        return ResponseEntity.ok(response);
    }

    /**
     * Executes MCP tool calls via HTTP POST.
     * 
     * @param request the MCP request containing tool call details
     * @return ResponseEntity containing the tool execution result
     */
    @PostMapping("/call")
    public ResponseEntity<McpResponse> callTool(@RequestBody McpRequest request) {
        log.info("MCP: Executing tool call - Method: {}", request.getMethod());

        try {
            McpResponse response = processToolCall(request);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("MCP: Error executing tool call", e);
            
            McpResponse errorResponse = McpResponse.builder()
                    .jsonrpc("2.0")
                    .id(request.getId())
                    .error(McpResponse.McpError.builder()
                            .code(-32603)
                            .message("Internal error: " + e.getMessage())
                            .build())
                    .build();
            
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Sends bulk emails to users registered in a specific year.
     * 
     * @param subject the email subject
     * @param templateName the email template name
     * @param targetYear the year to filter users by
     * @param activeUsersOnly whether to include only active users
     * @return ResponseEntity containing the operation result
     */
    @PostMapping("/bulk-email")
    public ResponseEntity<Map<String, Object>> sendBulkEmail(
            @RequestParam String subject,
            @RequestParam String templateName,
            @RequestParam Integer targetYear,
            @RequestParam(defaultValue = "true") Boolean activeUsersOnly) {

        log.info("MCP: Sending bulk email via REST - Year: {}, Subject: {}", targetYear, subject);

        Map<String, Object> params = new HashMap<>();
        params.put("subject", subject);
        params.put("templateName", templateName);
        params.put("targetYear", targetYear);
        params.put("activeUsersOnly", activeUsersOnly);

        Map<String, Object> result = mcpKafkaService.sendBulkEmail(params);
        return ResponseEntity.ok(result);
    }

    /**
     * Monitors Kafka health and statistics.
     * 
     * @param operation the monitoring operation type
     * @param topicName optional specific topic name
     * @return ResponseEntity containing monitoring results
     */
    @GetMapping("/monitor")
    public ResponseEntity<Map<String, Object>> monitorKafka(
            @RequestParam String operation,
            @RequestParam(required = false) String topicName) {

        log.info("MCP: Monitoring Kafka via REST - Operation: {}", operation);

        Map<String, Object> params = new HashMap<>();
        params.put("operation", operation);
        if (topicName != null) {
            params.put("topicName", topicName);
        }

        Map<String, Object> result = mcpKafkaService.monitorKafka(params);
        return ResponseEntity.ok(result);
    }

    /**
     * Gets user registration statistics.
     * 
     * @param year the year to get statistics for
     * @param activeOnly whether to count only active users
     * @return ResponseEntity containing user statistics
     */
    @GetMapping("/user-stats")
    public ResponseEntity<Map<String, Object>> getUserStats(
            @RequestParam Integer year,
            @RequestParam(defaultValue = "true") Boolean activeOnly) {

        log.info("MCP: Getting user stats via REST - Year: {}", year);

        Map<String, Object> params = new HashMap<>();
        params.put("year", year);
        params.put("activeOnly", activeOnly);

        Map<String, Object> result = mcpKafkaService.getUserStats(params);
        return ResponseEntity.ok(result);
    }

    /**
     * MCP server health check.
     * 
     * @return ResponseEntity indicating MCP server health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "MCP Kafka Email Server");
        health.put("version", "1.0.0");
        health.put("capabilities", Arrays.asList("bulk_email", "kafka_monitoring", "user_stats"));

        return ResponseEntity.ok(health);
    }

    /**
     * Processes tool call requests.
     */
    private McpResponse processToolCall(McpRequest request) {
        Map<String, Object> params = request.getParams();
        String toolName = (String) params.get("name");
        @SuppressWarnings("unchecked")
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");

        Object result;
        switch (toolName) {
            case "send_bulk_email":
                result = mcpKafkaService.sendBulkEmail(arguments);
                break;
            case "monitor_kafka":
                result = mcpKafkaService.monitorKafka(arguments);
                break;
            case "get_user_stats":
                result = mcpKafkaService.getUserStats(arguments);
                break;
            default:
                return McpResponse.builder()
                        .jsonrpc("2.0")
                        .id(request.getId())
                        .error(McpResponse.McpError.builder()
                                .code(-32602)
                                .message("Unknown tool: " + toolName)
                                .build())
                        .build();
        }

        Map<String, Object> response = new HashMap<>();
        response.put("content", Arrays.asList(Map.of(
                "type", "text",
                "text", result.toString()
        )));

        return McpResponse.builder()
                .jsonrpc("2.0")
                .id(request.getId())
                .result(response)
                .build();
    }
}
