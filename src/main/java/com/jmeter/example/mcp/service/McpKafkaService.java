package com.jmeter.example.mcp.service;

import com.jmeter.example.dto.BulkEmailRequest;
import com.jmeter.example.dto.EmailRequest;
import com.jmeter.example.dto.EmailResponse;
import com.jmeter.example.service.EmailService;
import com.jmeter.example.service.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartitionInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * MCP service for Kafka-related operations.
 * 
 * This service provides Kafka monitoring, topic management, and email
 * operations that can be called through the MCP protocol.
 * 
 * <AUTHOR> Dandu
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpKafkaService {

    private final EmailService emailService;
    private final UserRepository userRepository;
    private final KafkaAdmin kafkaAdmin;

    @Value("${kafka.topic.email}")
    private String emailTopic;

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    /**
     * Sends bulk emails to users registered in a specific year.
     * 
     * @param params MCP parameters containing email details
     * @return Map containing the operation result
     */
    public Map<String, Object> sendBulkEmail(Map<String, Object> params) {
        try {
            String subject = (String) params.get("subject");
            String templateName = (String) params.get("templateName");
            Integer targetYear = (Integer) params.get("targetYear");
            Boolean activeUsersOnly = (Boolean) params.getOrDefault("activeUsersOnly", true);

            log.info("MCP: Sending bulk email for year {} with subject: {}", targetYear, subject);

            // Create common template variables
            Map<String, Object> commonVariables = new HashMap<>();
            commonVariables.put("currentYear", LocalDate.now().getYear());
            commonVariables.put("companyName", "JMeter Application");
            commonVariables.put("targetYear", targetYear);

            BulkEmailRequest bulkEmailRequest = BulkEmailRequest.builder()
                    .subject(subject)
                    .templateName(templateName)
                    .targetYear(targetYear)
                    .activeUsersOnly(activeUsersOnly)
                    .commonTemplateVariables(commonVariables)
                    .priority(EmailRequest.EmailPriority.NORMAL)
                    .build();

            EmailResponse response = emailService.sendBulkEmailToYearlyUsers(bulkEmailRequest);

            Map<String, Object> result = new HashMap<>();
            result.put("success", response.getStatus() == EmailResponse.EmailStatus.QUEUED);
            result.put("status", response.getStatus().toString());
            result.put("message", response.getMessage());
            result.put("emailsQueued", response.getEmailsQueued());
            result.put("targetYear", targetYear);
            result.put("activeUsersOnly", activeUsersOnly);

            return result;

        } catch (Exception e) {
            log.error("MCP: Failed to send bulk email", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * Monitors Kafka topics, consumers, and health.
     * 
     * @param params MCP parameters containing monitoring operation type
     * @return Map containing monitoring results
     */
    public Map<String, Object> monitorKafka(Map<String, Object> params) {
        try {
            String operation = (String) params.get("operation");
            String topicName = (String) params.get("topicName");

            log.info("MCP: Monitoring Kafka - Operation: {}, Topic: {}", operation, topicName);

            switch (operation.toLowerCase()) {
                case "topics":
                    return getTopicInformation(topicName);
                case "health":
                    return getKafkaHealth();
                case "stats":
                    return getKafkaStats();
                default:
                    Map<String, Object> error = new HashMap<>();
                    error.put("success", false);
                    error.put("error", "Unknown operation: " + operation);
                    return error;
            }

        } catch (Exception e) {
            log.error("MCP: Failed to monitor Kafka", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * Gets user registration statistics by year.
     * 
     * @param params MCP parameters containing year and filters
     * @return Map containing user statistics
     */
    public Map<String, Object> getUserStats(Map<String, Object> params) {
        try {
            Integer year = (Integer) params.get("year");
            Boolean activeOnly = (Boolean) params.getOrDefault("activeOnly", true);

            log.info("MCP: Getting user stats for year: {}, activeOnly: {}", year, activeOnly);

            int userCount = userRepository.countUsersByRegistrationYear(year, activeOnly);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("year", year);
            result.put("activeOnly", activeOnly);
            result.put("userCount", userCount);
            result.put("message", String.format("Found %d users registered in %d", userCount, year));

            return result;

        } catch (Exception e) {
            log.error("MCP: Failed to get user stats", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * Gets information about Kafka topics.
     */
    private Map<String, Object> getTopicInformation(String specificTopic) throws ExecutionException, InterruptedException {
        try (AdminClient adminClient = AdminClient.create(kafkaAdmin.getConfigurationProperties())) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            if (specificTopic != null) {
                // Get specific topic information
                DescribeTopicsResult describeResult = adminClient.describeTopics(Collections.singletonList(specificTopic));
                TopicDescription description = describeResult.values().get(specificTopic).get();
                
                Map<String, Object> topicInfo = new HashMap<>();
                topicInfo.put("name", description.name());
                topicInfo.put("partitions", description.partitions().size());
                topicInfo.put("replicationFactor", description.partitions().get(0).replicas().size());
                
                List<Map<String, Object>> partitionInfo = new ArrayList<>();
                for (TopicPartitionInfo partition : description.partitions()) {
                    Map<String, Object> partInfo = new HashMap<>();
                    partInfo.put("partition", partition.partition());
                    partInfo.put("leader", partition.leader().id());
                    partInfo.put("replicas", partition.replicas().size());
                    partitionInfo.add(partInfo);
                }
                topicInfo.put("partitionDetails", partitionInfo);
                
                result.put("topic", topicInfo);
            } else {
                // List all topics
                ListTopicsResult listResult = adminClient.listTopics();
                Set<String> topicNames = listResult.names().get();
                result.put("topics", new ArrayList<>(topicNames));
                result.put("topicCount", topicNames.size());
            }

            return result;
        }
    }

    /**
     * Gets Kafka cluster health information.
     */
    private Map<String, Object> getKafkaHealth() {
        Map<String, Object> result = new HashMap<>();
        
        try (AdminClient adminClient = AdminClient.create(kafkaAdmin.getConfigurationProperties())) {
            // Try to list topics to check connectivity
            ListTopicsResult listResult = adminClient.listTopics();
            Set<String> topics = listResult.names().get();
            
            result.put("success", true);
            result.put("status", "HEALTHY");
            result.put("topicCount", topics.size());
            result.put("emailTopicExists", topics.contains(emailTopic));
            result.put("bootstrapServers", bootstrapServers);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("status", "UNHEALTHY");
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * Gets Kafka statistics and metrics.
     */
    private Map<String, Object> getKafkaStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("success", true);
            result.put("emailTopic", emailTopic);
            result.put("bootstrapServers", bootstrapServers);
            
            // Add more statistics as needed
            result.put("timestamp", new Date());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
