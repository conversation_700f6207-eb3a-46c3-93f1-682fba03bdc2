server.servlet.context-path=/jmeter/
server.hostUrl=http://localhost:3000


# DB Configurations
spring.datasource.url=****************************************
spring.datasource.username=jmeter
spring.datasource.password=jmeter
spring.datasource.driver-class-name=org.postgresql.Driver

# JWT Configuration
jwt.secret=yourSecretKey12345678901234567890123456789012
jwt.expiration=86400000

# Server configuration
server.port=8080

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=jmeter-email-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
spring.kafka.consumer.properties.spring.json.trusted.packages=*
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME:<EMAIL>}
spring.mail.password=${EMAIL_PASSWORD:your-app-password}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# Email Topics
kafka.topic.email=email-notifications

# Application Email Settings
app.email.from=${EMAIL_FROM:<EMAIL>}
app.email.enabled=${EMAIL_ENABLED:true}
