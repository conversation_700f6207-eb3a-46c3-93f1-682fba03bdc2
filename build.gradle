plugins {
    id 'org.springframework.boot' version '2.6.6'
    id 'jacoco'
    id 'nu.studer.jooq' version '7.1'
    id 'org.sonarqube' version '4.3.0.3225'
}

apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'jacoco'

group = 'com.chidhagni'
version = '0.0.1-SNAPSHOT'

sourceCompatibility = '11'
targetCompatibility = '11'

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://smoochorg.bintray.com' }
}

dependencies {
    testImplementation 'junit:junit:4.13.1'

    // Database driver for JOOQ
    jooqGenerator "org.postgresql:postgresql"

    // Oauth2
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'

    // Jsoup extracting data from HTML and manipulates
    implementation 'org.jsoup:jsoup:1.14.3'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Spring dependencies
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    runtimeOnly 'org.springframework.boot:spring-boot-starter-validation'

    // DB dependencies
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.liquibase:liquibase-core'
    implementation 'org.jooq:jooq'
    implementation 'com.zaxxer:HikariCP'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'

    // ApiDocs dependencies
    implementation "org.springdoc:springdoc-openapi-ui:1.6.7"
    implementation "org.springdoc:springdoc-openapi-security:1.6.7"

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-impl:0.11.2'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.11.2'
    implementation 'org.springframework.security:spring-security-oauth2-client:5.6.0'
    implementation 'org.springframework.security:spring-security-oauth2-jose:5.6.0'
    implementation 'org.springframework.security:spring-security-oauth2-resource-server:5.6.0'
    implementation 'com.auth0:java-jwt:3.18.2'

    implementation 'org.json:json:20210307'

    implementation 'org.springframework.boot:spring-boot-starter'
    runtimeOnly 'ch.qos.logback:logback-classic'

    implementation 'ch.qos.logback:logback-core:1.2.3'
    implementation 'ch.qos.logback:logback-classic:1.2.3'

    implementation 'org.jetbrains:annotations:17.0.0'

    testImplementation 'org.testcontainers:testcontainers:1.19.0'
    testImplementation 'org.testcontainers:postgresql:1.19.0'

    implementation 'com.jayway.jsonpath:json-path:2.9.0'

    // Kafka dependencies
    implementation 'org.springframework.kafka:spring-kafka'
    testImplementation 'org.springframework.kafka:spring-kafka-test'

    // Email dependencies
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

    testImplementation 'org.apache.jmeter:ApacheJMeter_core:5.4.3' // Compatible with Java 11
    testImplementation 'org.apache.jmeter:ApacheJMeter_http:5.4.3'
    testImplementation 'org.apache.jmeter:ApacheJMeter_java:5.4.3'}

compileJava {
    options.compilerArgs << "-Xlint:unchecked"
}

test {
    useJUnitPlatform()
    reports {
        junitXml.required.set(true)
        html.required.set(true)
    }
}

tasks.withType(Test) {
    testLogging.showStandardStreams = true
    afterTest { desc, result ->
        println "Executing test ${desc.name} [${desc.className}] with result: ${result.resultType}"
    }
    testLogging {
        events "passed", "skipped", "failed"
    }
}

task unitTest(type: Test) {
    useJUnitPlatform {
        maxParallelForks = Runtime.runtime.availableProcessors()
        include '**/*UTest.class'
    }
    failFast = true
}

task integrationTest(type: Test) {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
        include '**/*ITest.class'
    }
    failFast = true
}

task apiTest(type: Test) {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
        include '**/*ApiTest.class'
    }
    failFast = true
}

task jooqGen {
    dependsOn += 'generateJooq'
}
jooq {
    configurations {
        main {
            generateSchemaSourceOnCompilation = false
            generationTool {
                jdbc {
                    driver = 'org.postgresql.Driver'
                    url = '****************************************'
                    user = 'jmeter'
                    password = 'jmeter'
                    properties {
                        property {
                            key = 'useSSL'
                            value = 'false'
                        }
                    }
                }
                generator {
                    name = 'org.jooq.codegen.DefaultGenerator'
                    database {

                        forcedTypes {
                        }

                        name = 'org.jooq.meta.postgres.PostgresDatabase'
                        inputSchema = 'public'
                        outputSchemaToDefault = true
                        excludes = 'DATABASECHANGELOG|DATABASECHANGELOGLOCK|SHEDLOCK'
                    }
                    generate {
                        relations = false
                        deprecated = false
                        records = true
                        pojos = true
                        daos = true
                        springAnnotations = true
                        javaTimeTypes = true
                        fluentSetters = true
                        pojosEqualsAndHashCode = true
                    }

                    target {
                        packageName = 'com.chidhagni.jmeter.db.jooq'
                        directory = 'src/generated-db-entities/java/'
                    }
                }

            }
        }
    }
}

jacocoTestReport {
    dependsOn test
    executionData.setFrom(fileTree(dir: "$buildDir/jacoco", include: "**/*.exec"))

    reports {
        html.required.set(true)
        xml.required.set(true)
        csv.required.set(false)
    }

    classDirectories.setFrom(files(project.sourceSets.main.output))
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: ['com/chidhagni/jmeter/db/jooq/**'])
        }))
    }

    // Ensure jacocoTestReport runs after compileJava and processResources
    mustRunAfter compileJava
    mustRunAfter processResources
    dependsOn test
}

sonarqube {
    properties {
        property "sonar.coverage.jacoco.xmlReportPaths",
                "$buildDir/reports/jacoco/test/jacocoTestReport.xml"
        property "sonar.exclusions", "src/generated-db-entities/**, src/**/config/**"
    }
}

// Treat the generated sources as a separate source set so that Pull Requests are not confusing
sourceSets {
    main {
        java {
            srcDirs 'src/generated-db-entities/java'
        }
    }
}

bootRun {
    // The profile can be passed as ./gradlew clean bootRun -Dspring.profiles.active=dev
    systemProperties['spring.profiles.active'] = project.gradle.startParameter.systemPropertiesArgs['spring.profiles.active']
}

// Add this section to customize JAR naming based on the 'profile' property
bootJar {
    archiveFileName = "jmeter-${project.properties['profile']}.jar"
}